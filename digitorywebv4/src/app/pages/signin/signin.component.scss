:host {
  display: block;
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

// Professional inventory management background
.restaurant-signin-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg,
    #1a1a1a 0%,     // Deep charcoal
    #2d2d2d 25%,    // Dark gray
    #3a3a3a 50%,    // Medium gray
    #4a4a4a 75%,    // Light gray
    #5a5a5a 100%    // Lighter gray
  );

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(255, 140, 66, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 140, 66, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(135, 169, 107, 0.04) 0%, transparent 50%);
    pointer-events: none;
  }
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="10" cy="60" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="90" cy="40" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;

  .inventory-icon, .chart-icon, .database-icon {
    position: absolute;
    font-size: 3rem;
    opacity: 0.02;
    color: #ff8c42;
    animation: float 8s ease-in-out infinite;
  }

  .inventory-icon {
    top: 15%;
    left: 8%;
    animation-delay: 0s;
  }

  .chart-icon {
    top: 25%;
    right: 12%;
    animation-delay: 3s;
  }

  .database-icon {
    bottom: 20%;
    left: 15%;
    animation-delay: 6s;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

// Main signin card
.signin-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 24px 48px rgba(0, 0, 0, 0.3),
    0 12px 24px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  max-width: 420px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 140, 66, 0.2);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ff8c42, #87a96b, #ff8c42);
  }
}

// Header section
.restaurant-header {
  padding: 2rem 2rem 1.5rem;
  text-align: center;
  background: linear-gradient(135deg,
    rgba(255, 140, 66, 0.05) 0%,
    rgba(135, 169, 107, 0.03) 100%
  );
  border-bottom: 1px solid rgba(255, 140, 66, 0.1);
}

.logo-section {
  margin-bottom: 1.5rem;
}

.restaurant-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;

  .logo-image {
    max-width: 180px;
    height: auto;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }
  }

  .logo-subtitle {
    font-size: 0.9rem;
    color: #6b3e24;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    text-align: center;
    opacity: 0.8;
  }
}

.welcome-section {
  .welcome-title {
    font-size: 1.6rem;
    font-weight: 600;
    color: #2c2c2c;
    margin: 0 0 0.4rem 0;
  }

  .welcome-subtitle {
    font-size: 0.95rem;
    color: #666666;
    margin: 0 0 1.2rem 0;
    font-weight: 400;
    font-style: italic;
    line-height: 1.4;
  }

  .decorative-line {
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #ff8c42, #87a96b);
    margin: 0 auto;
    border-radius: 2px;
  }
}

// Form container
.form-container {
  padding: 2rem;
}

.restaurant-form {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.form-group {
  position: relative;
}

// Custom form field styling
.custom-form-field {
  width: 100%;

  ::ng-deep {
    .mat-mdc-form-field-outline {
      border-radius: 12px;
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-start,
    .mat-mdc-form-field-outline-end {
      border-radius: 12px;
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-gap {
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-thick {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-end,
      .mat-mdc-form-field-outline-gap {
        border-width: 3px;
        border-color: #ff8c42;
      }
    }

    .mat-mdc-form-field-outline:not(.mat-mdc-form-field-outline-thick) {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-end,
      .mat-mdc-form-field-outline-gap {
        border-color: rgba(107, 62, 36, 0.3);
      }
    }

    .mat-mdc-form-field-label {
      color: #6b3e24;
      font-weight: 500;
    }

    .mat-mdc-form-field-required-marker {
      color: #ff8c42;
    }

    .mdc-text-field--focused .mat-mdc-form-field-label {
      color: #ff8c42;
    }

    .mat-mdc-input-element {
      color: #2c1810;
      font-weight: 400;

      &::placeholder {
        color: rgba(107, 62, 36, 0.6);
        font-style: italic;
      }
    }

    .mat-icon {
      color: #6b3e24;

      &.password-toggle {
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff8c42;
        }
      }
    }

    .mat-mdc-form-field-prefix .mat-icon,
    .mat-mdc-form-field-suffix .mat-icon {
      margin-right: 8px;
    }
  }
}

// Form actions
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 0.8rem;
  justify-content: space-between;
  align-items: center;
}

// Direct button styling - override Material Design
button.cancel-btn {
  flex: 1;
  height: 48px !important;
  border-radius: 12px !important;
  border: 2px solid rgba(0, 0, 0, 0.23) !important;
  background: white !important;
  color: rgba(0, 0, 0, 0.87) !important;
  font-weight: 500 !important;
  font-size: 0.95rem !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  box-shadow: none !important;

  &:hover {
    border-color: #ff8c42 !important;
    background-color: rgba(255, 140, 66, 0.04) !important;
    color: #ff8c42 !important;
  }

  &:focus {
    outline: 2px solid rgba(255, 140, 66, 0.3) !important;
    outline-offset: 2px !important;
  }
}

button.signin-btn {
  flex: 2;
  height: 48px !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #ff8c42 0%, #e6732a 100%) !important;
  color: white !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 12px rgba(255, 140, 66, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #e6732a 0%, #cc5a12 100%) !important;
    box-shadow: 0 6px 16px rgba(255, 140, 66, 0.4) !important;
    transform: translateY(-2px) !important;
  }

  &:disabled {
    background: rgba(0, 0, 0, 0.12) !important;
    color: rgba(0, 0, 0, 0.26) !important;
    box-shadow: none !important;
    transform: none !important;
    cursor: not-allowed !important;
  }

  &:focus:not(:disabled) {
    outline: 2px solid rgba(255, 140, 66, 0.3) !important;
    outline-offset: 2px !important;
  }

  .loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;

    .spinning {
      animation: spin 1s linear infinite;
      font-size: 18px !important;
      width: 18px !important;
      height: 18px !important;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Footer
.restaurant-footer {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg,
    rgba(44, 24, 16, 0.03) 0%,
    rgba(107, 62, 36, 0.05) 100%
  );
  border-top: 1px solid rgba(255, 140, 66, 0.1);
  text-align: center;
}

.footer-content {
  .copyright {
    font-size: 0.85rem;
    color: #6b3e24;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }

  .tagline {
    font-size: 0.8rem;
    color: rgba(107, 62, 36, 0.7);
    margin: 0;
    font-style: italic;
    font-weight: 400;
  }
}

// Responsive design
@media (max-width: 768px) {
  .restaurant-signin-container {
    padding: 0.5rem;
  }

  .signin-card {
    max-width: 100%;
    margin: 0;
    border-radius: 16px;
    max-height: 95vh;
  }

  .restaurant-header {
    padding: 1.5rem 1.2rem 1rem;
  }

  .restaurant-logo {
    .logo-image {
      max-width: 160px;
    }

    .logo-subtitle {
      font-size: 0.85rem;
    }
  }

  .welcome-section {
    .welcome-title {
      font-size: 1.5rem;
    }

    .welcome-subtitle {
      font-size: 0.9rem;
    }
  }

  .form-container {
    padding: 1.2rem;
  }

  .restaurant-form {
    gap: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.8rem;
    margin-top: 0.5rem;

    .cancel-btn,
    .signin-btn {
      flex: none;
      width: 100%;
    }
  }

  .restaurant-footer {
    padding: 1.2rem;
  }

  .decorative-elements {
    .inventory-icon, .chart-icon, .database-icon {
      font-size: 2.5rem;
    }
  }
}

@media (max-width: 480px) {
  .restaurant-header {
    padding: 1.5rem 1rem;
  }

  .restaurant-logo {
    .logo-image {
      max-width: 140px;
    }

    .logo-subtitle {
      font-size: 0.75rem;
      letter-spacing: 0.5px;
    }
  }

  .form-container {
    padding: 1rem;
  }

  .restaurant-footer {
    padding: 1rem;
  }
}

// Enhanced hover effects and animations
.signin-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 40px 80px rgba(0, 0, 0, 0.35),
      0 20px 40px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

// Focus states for accessibility
.custom-form-field {
  ::ng-deep {
    .mat-mdc-input-element:focus {
      outline: none;
    }
  }
}

// Loading state enhancements
.signin-btn {
  .loading-spinner {
    .spinning {
      color: rgba(255, 255, 255, 0.9);
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  // Ensure loading state is properly styled
  &.loading {
    ::ng-deep .mat-mdc-button-base {
      pointer-events: none;
    }
  }
}

// Ensure proper button spacing and alignment
.form-actions {
  button {
    min-width: 120px;
    padding: 0 24px;
  }
}

// Additional visual enhancements
.restaurant-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff8c42, transparent);
}