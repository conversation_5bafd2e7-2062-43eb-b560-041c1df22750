:host {
  display: block;
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

// Restaurant-themed background
.restaurant-signin-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  background: linear-gradient(135deg,
    #2c1810 0%,     // Dark coffee brown
    #3d2817 25%,    // Rich brown
    #4a2c1a 50%,    // Medium brown
    #5d3621 75%,    // Warm brown
    #6b3e24 100%    // Light brown
  );

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(255, 140, 66, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 140, 66, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(135, 169, 107, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="10" cy="60" r="0.5" fill="%23ffffff" opacity="0.03"/><circle cx="90" cy="40" r="0.5" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;

  .chef-hat-icon, .utensils-icon, .plate-icon {
    position: absolute;
    font-size: 4rem;
    opacity: 0.03;
    color: #ff8c42;
    animation: float 6s ease-in-out infinite;
  }

  .chef-hat-icon {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  .utensils-icon {
    top: 20%;
    right: 15%;
    animation-delay: 2s;
  }

  .plate-icon {
    bottom: 15%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

// Main signin card
.signin-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.3),
    0 16px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  max-width: 480px;
  width: 100%;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 140, 66, 0.2);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff8c42, #87a96b, #ff8c42);
  }
}

// Restaurant header section
.restaurant-header {
  padding: 3rem 2.5rem 2rem;
  text-align: center;
  background: linear-gradient(135deg,
    rgba(255, 140, 66, 0.05) 0%,
    rgba(135, 169, 107, 0.03) 100%
  );
  border-bottom: 1px solid rgba(255, 140, 66, 0.1);
}

.logo-section {
  margin-bottom: 2rem;
}

.restaurant-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  .logo-icon {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
    filter: drop-shadow(0 4px 8px rgba(255, 140, 66, 0.3));
  }

  .restaurant-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c1810;
    margin: 0;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-family: 'Montserrat', serif;
  }

  .logo-subtitle {
    font-size: 0.9rem;
    color: #6b3e24;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
  }
}

.welcome-section {
  .welcome-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c1810;
    margin: 0 0 0.5rem 0;
  }

  .welcome-subtitle {
    font-size: 1rem;
    color: #6b3e24;
    margin: 0 0 1.5rem 0;
    font-weight: 400;
  }

  .decorative-line {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #ff8c42, #87a96b);
    margin: 0 auto;
    border-radius: 2px;
  }
}

// Form container
.form-container {
  padding: 2.5rem;
}

.restaurant-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

// Custom form field styling
.custom-form-field {
  width: 100%;

  ::ng-deep {
    .mat-mdc-form-field-outline {
      border-radius: 12px;
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-start,
    .mat-mdc-form-field-outline-end {
      border-radius: 12px;
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-gap {
      border-width: 2px;
    }

    .mat-mdc-form-field-outline-thick {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-end,
      .mat-mdc-form-field-outline-gap {
        border-width: 3px;
        border-color: #ff8c42;
      }
    }

    .mat-mdc-form-field-outline:not(.mat-mdc-form-field-outline-thick) {
      .mat-mdc-form-field-outline-start,
      .mat-mdc-form-field-outline-end,
      .mat-mdc-form-field-outline-gap {
        border-color: rgba(107, 62, 36, 0.3);
      }
    }

    .mat-mdc-form-field-label {
      color: #6b3e24;
      font-weight: 500;
    }

    .mat-mdc-form-field-required-marker {
      color: #ff8c42;
    }

    .mdc-text-field--focused .mat-mdc-form-field-label {
      color: #ff8c42;
    }

    .mat-mdc-input-element {
      color: #2c1810;
      font-weight: 400;

      &::placeholder {
        color: rgba(107, 62, 36, 0.6);
        font-style: italic;
      }
    }

    .mat-icon {
      color: #6b3e24;

      &.password-toggle {
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #ff8c42;
        }
      }
    }

    .mat-mdc-form-field-prefix .mat-icon,
    .mat-mdc-form-field-suffix .mat-icon {
      margin-right: 8px;
    }
  }
}

// Form actions
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: space-between;
}

.cancel-btn {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  border: 2px solid rgba(107, 62, 36, 0.3);
  color: #6b3e24;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    border-color: #6b3e24;
    background-color: rgba(107, 62, 36, 0.05);
  }

  .mat-icon {
    margin-right: 8px;
  }
}

.signin-btn {
  flex: 2;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff8c42 0%, #e6732a 100%);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(255, 140, 66, 0.3);
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #e6732a 0%, #cc5a12 100%);
    box-shadow: 0 6px 16px rgba(255, 140, 66, 0.4);
    transform: translateY(-2px);
  }

  &:disabled {
    background: rgba(107, 62, 36, 0.3);
    color: rgba(255, 255, 255, 0.6);
    box-shadow: none;
  }

  .mat-icon {
    margin-right: 8px;
  }

  .loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;

    .spinning {
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Restaurant footer
.restaurant-footer {
  padding: 2rem 2.5rem;
  background: linear-gradient(135deg,
    rgba(44, 24, 16, 0.03) 0%,
    rgba(107, 62, 36, 0.05) 100%
  );
  border-top: 1px solid rgba(255, 140, 66, 0.1);
  text-align: center;
}

.footer-content {
  .copyright {
    font-size: 0.85rem;
    color: #6b3e24;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
  }

  .tagline {
    font-size: 0.8rem;
    color: rgba(107, 62, 36, 0.7);
    margin: 0;
    font-style: italic;
    font-weight: 400;
  }
}

// Responsive design
@media (max-width: 768px) {
  .restaurant-signin-container {
    padding: 1rem 0.5rem;
  }

  .signin-card {
    max-width: 100%;
    margin: 0 0.5rem;
    border-radius: 16px;
  }

  .restaurant-header {
    padding: 2rem 1.5rem 1.5rem;
  }

  .restaurant-logo {
    .logo-icon {
      font-size: 2.5rem;
    }

    .restaurant-name {
      font-size: 2rem;
    }
  }

  .welcome-section {
    .welcome-title {
      font-size: 1.5rem;
    }

    .welcome-subtitle {
      font-size: 0.9rem;
    }
  }

  .form-container {
    padding: 1.5rem;
  }

  .restaurant-form {
    gap: 1.2rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.8rem;

    .cancel-btn,
    .signin-btn {
      flex: none;
      width: 100%;
    }
  }

  .restaurant-footer {
    padding: 1.5rem;
  }

  .decorative-elements {
    .chef-hat-icon, .utensils-icon, .plate-icon {
      font-size: 2.5rem;
    }
  }
}

@media (max-width: 480px) {
  .restaurant-header {
    padding: 1.5rem 1rem;
  }

  .restaurant-logo {
    .restaurant-name {
      font-size: 1.8rem;
      letter-spacing: 1px;
    }

    .logo-subtitle {
      font-size: 0.8rem;
    }
  }

  .form-container {
    padding: 1rem;
  }

  .restaurant-footer {
    padding: 1rem;
  }
}

// Enhanced hover effects and animations
.signin-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow:
      0 40px 80px rgba(0, 0, 0, 0.35),
      0 20px 40px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }
}

// Focus states for accessibility
.custom-form-field {
  ::ng-deep {
    .mat-mdc-input-element:focus {
      outline: none;
    }
  }
}

// Loading state enhancements
.signin-btn {
  .loading-spinner {
    .spinning {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

// Additional visual enhancements
.restaurant-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff8c42, transparent);
}