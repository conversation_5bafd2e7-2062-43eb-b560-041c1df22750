.smart-dashboard-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  height: 100vh; // Take full viewport height
  overflow: hidden; // Prevent outer scrollbar

  // ===== MATERIAL DESIGN THEME OVERRIDES =====
  ::ng-deep {
    // Form fields
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        height: 36px;
        min-height: 36px;
      }

      .mat-mdc-form-field-infix {
        padding: 6px 12px;
        min-height: 24px;
        border-top: none;
      }

      .mat-mdc-form-field-flex {
        align-items: center;
        height: 36px;
      }

      .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }

      .mat-mdc-form-field-outline {
        color: #dee2e6;
      }

      .mat-mdc-form-field-outline-thick {
        color: #ffb366;
      }

      .mat-mdc-form-field-label {
        color: #666;
        font-size: 13px;
        top: 18px;
      }

      &.mat-focused .mat-mdc-form-field-label {
        color: #ffb366;
      }

      &.mat-form-field-should-float .mat-mdc-form-field-label {
        transform: translateY(-12px) scale(0.75);
      }
    }

    // Select dropdowns
    .mat-mdc-select {
      .mat-mdc-select-trigger {
        height: 36px;
        display: flex;
        align-items: center;
      }

      .mat-mdc-select-value {
        font-size: 13px;
        line-height: 24px;
      }

      .mat-mdc-select-arrow {
        color: #ffb366;
      }
    }

    // Select panel
    .mat-mdc-select-panel .mat-mdc-option {
      height: 32px;
      line-height: 32px;
      font-size: 13px;
      padding: 0 16px;

      &.mat-mdc-option-active {
        background: rgba(255, 179, 102, 0.1);
        color: #ffb366;
      }

      &:hover {
        background: rgba(255, 179, 102, 0.05);
      }
    }

    // Input elements
    .mat-mdc-input-element {
      font-size: 13px;
      height: 24px;
      line-height: 24px;
    }

    // Date picker
    .mat-datepicker-toggle .mat-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .mat-datepicker-content {
      .mat-calendar-header {
        background: #ffb366;
        color: white;
      }

      .mat-calendar-body-selected {
        background-color: #ffb366;
        color: white;
      }

      .mat-calendar-body-today:not(.mat-calendar-body-selected) {
        border-color: #ffb366;
      }
    }

    // Buttons
    .mat-mdc-raised-button,
    .mat-mdc-outlined-button {
      height: 32px;
      line-height: 32px;
      padding: 0 12px;
      font-size: 13px;

      &.mat-primary {
        background-color: #ffb366;
        color: white;

        &:hover {
          background-color: #ffa64d;
        }
      }
    }

    .mat-mdc-outlined-button.mat-primary {
      border-color: #ffb366;
      color: #ffb366;
      background-color: transparent;

      &:hover {
        background-color: rgba(255, 179, 102, 0.05);
      }
    }
  }

  // Main Layout Container
  .main-layout {
    display: flex;
    height: calc(100vh - 40px); // Full height minus actual toolbar height (40px)
    overflow: hidden; // Prevent outer scrollbar

    // Left Sidebar
    .left-sidebar {
      width: 250px;
      background: linear-gradient(135deg, #ffffff 0%, #fff5f0 100%);
      border-right: 1px solid #ffe0cc;
      padding: 12px 12px 60px 12px; // Add bottom padding for toggle switch
      box-shadow: 2px 0 4px rgba(255, 179, 102, 0.08);
      height: 100%; // Take full available height
      overflow-y: auto; // Allow scrolling within sidebar if needed
      position: relative; // For absolute positioned toggle switch

      // Custom scrollbar styling to match right panel
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #fff2e6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ffb366;
        border-radius: 3px;

        &:hover {
          background: #ffa64d;
        }
      }

      // Dashboard Selection
      .dashboard-selection {
        margin-bottom: 20px;

        .dashboard-dropdown {
          width: 100%;

          ::ng-deep .mat-mdc-text-field-wrapper {
            background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

            &:hover {
              border-color: #ffb366;
              box-shadow: 0 2px 6px rgba(255, 179, 102, 0.1);
            }
          }

          ::ng-deep .mat-mdc-form-field-outline,
          ::ng-deep .mat-mdc-form-field-outline-thick {
            display: none;
          }

          ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
            border-color: #ffb366;
            box-shadow: 0 3px 8px rgba(255, 179, 102, 0.15);
          }
        }
      }

      // Filters Section
      .filters-section {
        .filters-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0 0 16px 0;
          font-size: 15px;
          font-weight: 600;
          color: #333;

          mat-icon {
            color: #ffb366;
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }

        .filter-group {
          margin-bottom: 10px;

          .filter-label {
            margin: 0 0 8px 0;
            font-size: 13px;
            font-weight: 500;
            color: #333;
          }

          .filter-field {
            width: 100%;
            margin-bottom: 6px;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 4px;
            }
          }
        }

        .date-range-group {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }



        .filter-actions {
          margin-top: 20px;
          padding-top: 16px;
          border-top: 1px solid #e9ecef;
          display: flex;
          gap: 8px;

          .search-btn,
          .reset-filters-btn {
            flex: 1;
            padding: 8px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .search-btn {
            background-color: white;
            color: #ffb366;
            border: 2px solid #ffb366;
            font-weight: 600;

            &:hover {
              background-color: #fff8f5;
              border-color: #ffa64d;
              color: #ffa64d;
            }
          }

          .reset-filters-btn {
            color: #6c757d;
            border-color: #dee2e6;

            &:hover {
              background-color: #f8f9fa;
              border-color: #adb5bd;
            }
          }
        }
      }
    }

    // Dashboard Mode Info Section
    .dashboard-mode-section {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
      border-top: 2px solid #ffb366;
      padding: 16px;
      box-shadow: 0 -2px 8px rgba(255, 179, 102, 0.15);

      .mode-label {
        font-size: 13px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '⚙️';
          font-size: 12px;
        }
      }

      .mode-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .current-mode,
        .ai-mode-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
        }

        .current-mode {
          background: rgba(255, 179, 102, 0.1);
          color: #ffb366;
          border: 1px solid rgba(255, 179, 102, 0.3);
        }

        .ai-mode-info {
          background: rgba(173, 181, 189, 0.1);
          color: #6c757d;
          border: 1px solid rgba(173, 181, 189, 0.3);

          .disabled-icon,
          .disabled-text {
            color: #adb5bd;
          }
        }
      }
    }

    // Right Content Area
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f8f9fa;
      height: 100%; // Take full available height
      overflow: hidden; // Prevent outer scrollbar

      // AI Assistant Header
      .ai-assistant-header {
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
        border-bottom: 1px solid #e9ecef;
        padding: 10px 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        min-height: 50px;
        box-shadow: 0 1px 3px rgba(255, 179, 102, 0.05);

        .assistant-info {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;

          .assistant-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            transition: color 0.3s ease;

            &.disabled {
              color: #adb5bd;
            }
          }

          .assistant-text {
            display: flex;
            align-items: center;
            gap: 10px;

            .assistant-title {
              font-size: 14px;
              font-weight: 600;
              color: #333;
              white-space: nowrap;
            }

            .assistant-status {
              font-size: 10px;
              font-weight: 500;
              padding: 2px 8px;
              border-radius: 12px;
              display: inline-block;
              white-space: nowrap;

              &.disabled {
                color: #adb5bd;
                background: #f8f9fa;
              }
            }
          }
        }

        .search-container {
          flex: 1;
          min-width: 0;
          max-width: calc(100% - 200px);

          .search-field {
            width: 100%;

            ::ng-deep .mat-mdc-text-field-wrapper {
              background-color: white;
              border-radius: 24px;
              height: 36px;
              border: 1px solid #e9ecef;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
              transition: all 0.3s ease;

              &:hover:not(.disabled) {
                border-color: #ffb366;
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }
            }

            ::ng-deep .mat-mdc-form-field-outline,
            ::ng-deep .mat-mdc-form-field-outline-thick {
              display: none;
            }

            ::ng-deep .mat-mdc-form-field-infix {
              padding: 6px 16px;
              border-top: none;
            }

            ::ng-deep .mat-mdc-form-field-flex {
              align-items: center;
              height: 36px;
            }

            ::ng-deep input {
              font-size: 14px;
              color: #333;

              &::placeholder {
                color: #999;
                font-size: 14px;
              }
            }

            ::ng-deep .mat-focused .mat-mdc-text-field-wrapper {
              border-color: #ffb366;
              box-shadow: 0 4px 16px rgba(255, 179, 102, 0.15);
            }

            .search-icon {
              color: #999;
              cursor: pointer;
              font-size: 16px;
              transition: color 0.2s ease;

              &:hover:not(.disabled) {
                color: #ffb366;
              }

              &.disabled {
                color: #adb5bd;
                cursor: not-allowed;
              }
            }

            &.disabled {
              ::ng-deep .mat-mdc-text-field-wrapper {
                background-color: #f8f9fa;
                border-color: #e9ecef;
                box-shadow: none;

                &:hover {
                  border-color: #e9ecef;
                  box-shadow: none;
                }
              }

              ::ng-deep input {
                color: #adb5bd;
                cursor: not-allowed;
              }

              ::ng-deep input::placeholder {
                color: #adb5bd;
              }
            }
          }
        }
      }

      // Dashboard Content Area
      .dashboard-content-area {
        padding: 20px;
        flex: 1; // Take remaining height after search header
        overflow-y: auto; // Only this area should scroll

        // Custom scrollbar styling
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #ffb366;
          border-radius: 3px;

          &:hover {
            background: #ffa64d;
          }
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          gap: 20px;
          background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
          border-radius: 12px;
          border: 1px solid #ffe0cc;
          margin: 20px;
          box-shadow: 0 4px 12px rgba(255, 179, 102, 0.08);

          ::ng-deep .mat-mdc-progress-spinner {
            .mdc-circular-progress__determinate-circle,
            .mdc-circular-progress__indeterminate-circle-graphic {
              stroke: #ffb366 !important;
            }
          }

          p {
            color: #333;
            font-size: 16px;
            font-weight: 500;
            margin: 0;
            text-align: center;
            background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .dashboard-grid {
          .summary-cards-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 20px;

            .summary-card {
              background: white;
              border-radius: 8px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;

              &:hover {
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
              }

              .card-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;

                .card-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: linear-gradient(135deg, #ffb366 0%, #ffc999 100%);

                  mat-icon {
                    font-size: 20px;
                    width: 20px;
                    height: 20px;
                    color: white;
                  }
                }

                .card-info {
                  flex: 1;

                  .card-value {
                    font-size: 20px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    line-height: 1.2;
                  }

                  .card-label {
                    font-size: 12px;
                    color: #6c757d;
                    font-weight: 500;
                    line-height: 1.3;
                  }
                }
              }
            }
          }

          .charts-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 16px;

            .chart-card {
              background: white;
              border-radius: 12px;
              border: 1px solid #e9ecef;
              transition: box-shadow 0.2s ease;
              height: 400px;

              &:hover {
                box-shadow: 0 4px 12px rgba(255, 179, 102, 0.1);
              }

              &.full-width {
                grid-column: span 12;
              }

              &.half-width {
                grid-column: span 6;
              }

              &.third-width {
                grid-column: span 4;
              }

              .chart-title {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                margin: 0;
              }

              .chart-container {
                position: relative;
                height: 320px;
                padding: 16px;

                canvas {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          text-align: center;
          color: #666;

          .empty-icon {
            font-size: 64px;
            width: 64px;
            height: 64px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
          }

          p {
            margin: 0 0 24px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1400px) {
  .smart-dashboard-container .dashboard-grid {
    .summary-cards-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid .chart-card.half-width,
    .charts-grid .chart-card.third-width {
      grid-column: span 12;
    }
  }
}

@media (max-width: 1024px) {
  .smart-dashboard-container {
    .ai-assistant-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .left-sidebar {
      width: 220px;
    }
  }
}

@media (max-width: 768px) {
  .smart-dashboard-container .main-layout {
    flex-direction: column;

    .left-sidebar {
      width: 100%;
      border-right: none;
      border-bottom: 1px solid #e9ecef;
      padding: 12px;
    }

    .right-content {
      .ai-assistant-header {
        flex-direction: column;
        gap: 12px;
        padding: 12px 16px;
      }

      .dashboard-content-area {
        padding: 12px;

        .dashboard-grid {
          .summary-cards-row {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .charts-grid {
            gap: 12px;

            .chart-card {
              grid-column: span 12;
            }
          }
        }
      }
    }
  }
}